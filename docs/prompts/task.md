Of course, Cong. Building a serverless, AI-enhanced portfolio tracker is an excellent project. Using an AI agency like lovable.dev can streamline the development process. Let's structure the idea, outline the necessary considerations, and define the prompts you'll need.

### Revised Question

"I want to develop a serverless portfolio tracking application using React, Supabase, and TradingView's Lightweight Charts, with development facilitated by the AI agency, lovable.dev. Please provide a comprehensive project plan that includes:

1.  A refined application concept.
2.  Critical technical and architectural considerations.
3.  A logical breakdown of the necessary UI screens.
4.  Specific, actionable prompts to feed the AI agency for each development phase."

---

### 1. The Application Idea: "Signal"

Let's call the application "Signal". The core idea is to create a minimalist, high-performance portfolio tracker that focuses on one thing: providing clear technical signals on a user's holdings. It cuts through the noise of news and social media, focusing purely on price action and technical indicators like the MACD.

The long-term vision is for "Signal" to evolve. After implementing the MACD, you can introduce other indicators (RSI, Bollinger Bands). The "AI-based signals" you mentioned would be the ultimate goal: the app could learn from historical price action and indicator patterns to provide predictive insights, moving beyond simple tracking to active intelligence.

### 2. Key Considerations

Discipline in architecture is paramount for long-term success. Here is what you must consider:

- **Data Modeling (Supabase):**
  - `profiles`: A table to store user information, linked to `auth.users`.
  - `portfolios`: Links users to their portfolios (e.g., `id`, `user_id`, `name`).
  - `assets`: A master list of trackable assets (e.g., `id`, `symbol`, `name`, `asset_type` like 'CRYPTO' or 'STOCK').
  - `holdings`: Connects a portfolio to an asset, storing the quantity (e.g., `id`, `portfolio_id`, `asset_id`, `quantity`).
  - `price_data_daily`: Stores daily OHLCV (Open, High, Low, Close, Volume) data for each asset. Partition this table by date for performance.
- **Data Ingestion:**
  - How will you get the price data? You'll need a reliable financial data API (e.g., Alpha Vantage, Finnhub).
  - You must create a serverless function (`Supabase Edge Functions`) that runs on a schedule (e.g., daily using `pg_cron`). This function will fetch the latest price data from your chosen API and update your `price_data_daily` table. This is the most critical backend component.
- **API Design (Serverless Functions):**
  - Your React application will not query the database directly. It will call serverless functions.
  - `GET /api/portfolio`: Fetches the user's portfolio, holdings, and the latest price for each asset.
  - `GET /api/chart-data?assetId=X&timeframe=Y`: Fetches the historical price data for a specific asset, aggregated for the requested timeframe (1D, 2D, 3D, 1W). This function will contain the logic to process the raw daily data into the correct timeframe.
- **Free-Tier Limitations:**
  - **Supabase:** The free tier has database size and API request limits. Your data ingestion strategy must be efficient. Don't store tick-by-tick data; daily data is sufficient for your stated timeframes.
  - **Financial Data API:** Most free financial APIs have strict rate limits. Your daily data fetch must be robust enough to handle potential API failures and retries.
- **Security:**
  - Implement Row Level Security (RLS) in Supabase from day one. Users must only be able to see and edit their own data. This is non-negotiable.

### 3. Application Screens

1.  **Dashboard Screen (Home):**
    - **Purpose:** An overview of the user's total portfolio value and a list of all assets held.
    - **Components:**
      - A chart showing the total portfolio value over time.
      - A summary card displaying current total value, 24h change, and best/worst performer.
      - A list/table of individual holdings, showing the asset name, symbol, quantity, current price, and 24h change.
      - Clicking any asset in the list navigates to the "Asset Detail Screen".
2.  **Asset Detail Screen:**
    - **Purpose:** A deep dive into a single asset.
    - **Components:**
      - Header with asset name and symbol.
      - The main **Lightweight Chart** component.
      - Timeframe selector buttons (1D, 2D, 3D, 1W) that update the chart.
      - The **MACD indicator** displayed in a separate pane below the main price chart.
3.  **Settings Screen:**
    - **Purpose:** User profile and application settings.
    - **Components:**
      - Manage API keys for financial data (if you choose to let users add their own).
      - Theme selection (Light/Dark).
      - Logout button.

### 4. Prompts for the AI Agency (lovable.dev)

Provide clear, staged prompts. Do not ask it to build the entire application at once.

**Phase 1: Project Setup & Backend**

**Prompt 1: Initial Setup**
"Initialize a new project named 'Signal'.

- Frontend: Use the React + TypeScript template.
- Backend: Use Supabase.
- Install the following additional dependencies in the React app: `lightweight-charts`, `tailwind-css`.
- Configure Tailwind CSS for the project."

**Prompt 2: Supabase Schema**
"Create the database schema in Supabase. Define the following tables with Row Level Security (RLS) enabled for user data isolation:

1.  `profiles`: `id` (references auth.users), `username`, `avatar_url`. RLS: Users can only see and update their own profile.
2.  `assets`: `id`, `created_at`, `symbol` (text, unique), `name` (text), `asset_type` (text, e.g., 'CRYPTO'). RLS: Publicly readable.
3.  `portfolios`: `id`, `user_id` (references profiles.id), `name`. RLS: Users can only manage their own portfolios.
4.  `holdings`: `id`, `portfolio_id` (references portfolios.id), `asset_id` (references assets.id), `quantity` (numeric). RLS: Users can only manage holdings within their own portfolios.
5.  `price_data_daily`: `id`, `asset_id` (references assets.id), `date` (date), `open`, `high`, `low`, `close`, `volume` (all numeric). RLS: Publicly readable."

**Phase 2: Frontend Component Development**

**Prompt 3: Chart Component**
"Create a reusable React component named `AssetChart`.

- **File:** `src/components/AssetChart.tsx`
- **Props:** It should accept a prop `data` of type `CandlestickData[]` from the `lightweight-charts` library and a `macdData` prop of type `HistogramData[]`.
- **Functionality:**
  1.  Initialize a Lightweight Chart instance inside a `useEffect` hook.
  2.  Render a Candlestick series for the main price data.
  3.  Render a Histogram series in a separate pane below the price series for the MACD data.
  4.  The chart should be responsive and fill its parent container.
  5.  Implement a dark theme for the chart.
  6.  Ensure the chart object is properly cleaned up on component unmount to prevent memory leaks."

**Prompt 4: Dashboard Page**
"Create the Dashboard page.

- **File:** `src/pages/Dashboard.tsx`
- **Functionality:**
  1.  Fetch data from a placeholder Supabase serverless function `GET /api/portfolio`.
  2.  Display the total portfolio value and 24h change.
  3.  Render a list of assets from the fetched data. Each item in the list should display the asset's name, the quantity held, and its current value.
  4.  The page should have a clean, modern UI using Tailwind CSS."

**Phase 3: Logic & Data Integration**

**Prompt 5: Serverless Function for Chart Data**
"Create a Supabase Edge Function named `get-chart-data`.

- **Language:** TypeScript
- **Logic:**
  1.  It should accept `assetId` and `timeframe` (e.g., '1D', '2D', '3D', '1W') as query parameters.
  2.  Fetch daily price data for the given `assetId` from the `price_data_daily` table.
  3.  Implement logic to aggregate the daily data based on the `timeframe`. For '2D', combine two days of data; for '1W', combine 5 trading days.
  4.  Calculate the MACD indicator values from the aggregated price data. The MACD requires three parameters: fast period (12), slow period (26), and signal period (9).
  5.  Return a JSON object containing two arrays: one for the OHLC price candlesticks and one for the MACD histogram data, formatted correctly for the `AssetChart` component."

By breaking down the project into these logical, well-defined prompts, you provide the AI agency with clear instructions, adhering to best practices and ensuring the final code is high-quality, type-safe, and well-structured.
