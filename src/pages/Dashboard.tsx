import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Asset<PERSON>hart } from "@/components/AssetChart";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowUpIcon,
  ArrowDownIcon,
  TrendingUp,
  TrendingDown,
  DollarSign,
} from "lucide-react";
import { CandlestickData, HistogramData, UTCTimestamp } from "lightweight-charts";

// Mock data types
interface Holding {
  id: string;
  symbol: string;
  name: string;
  quantity: number;
  currentPrice: number;
  change24h: number;
  value: number;
}

interface PortfolioSummary {
  totalValue: number;
  change24h: number;
  changePercent24h: number;
  bestPerformer: { symbol: string; change: number };
  worstPerformer: { symbol: string; change: number };
}

// Generate fake portfolio value over time
const generatePortfolioData = (): CandlestickData[] => {
  const data: CandlestickData[] = [];
  let baseValue = 25000;

  for (let i = 0; i < 30; i++) {
    const time = new Date(2024, 0, 1 + i).getTime() / 1000;
    const volatility = 0.015;
    const change = (Math.random() - 0.5) * volatility * baseValue;

    const open = baseValue;
    const close = baseValue + change;
    const high = Math.max(open, close) + Math.random() * 0.005 * baseValue;
    const low = Math.min(open, close) - Math.random() * 0.005 * baseValue;

    data.push({
      time: time as UTCTimestamp,
      open,
      high,
      low,
      close,
    });

    baseValue = close;
  }

  return data;
};

// Generate fake portfolio performance indicators
const generatePerformanceData = (): HistogramData[] => {
  const data: HistogramData[] = [];

  for (let i = 0; i < 30; i++) {
    const time = new Date(2024, 0, 1 + i).getTime() / 1000;
    const value = Math.sin(i * 0.2) * 500 + Math.random() * 200;

    data.push({
      time: time as UTCTimestamp,
      value,
      color: value > 0 ? "#22c55e" : "#ef4444",
    });
  }

  return data;
};

// Mock holdings data
const mockHoldings: Holding[] = [
  {
    id: "1",
    symbol: "BTC",
    name: "Bitcoin",
    quantity: 0.5,
    currentPrice: 43500,
    change24h: 2.3,
    value: 21750,
  },
  {
    id: "2",
    symbol: "ETH",
    name: "Ethereum",
    quantity: 2.8,
    currentPrice: 2650,
    change24h: -1.2,
    value: 7420,
  },
  {
    id: "3",
    symbol: "AAPL",
    name: "Apple Inc.",
    quantity: 15,
    currentPrice: 185.5,
    change24h: 0.8,
    value: 2782.5,
  },
  {
    id: "4",
    symbol: "GOOGL",
    name: "Alphabet Inc.",
    quantity: 8,
    currentPrice: 142.3,
    change24h: -0.5,
    value: 1138.4,
  },
];

// Calculate portfolio summary
const calculatePortfolioSummary = (holdings: Holding[]): PortfolioSummary => {
  const totalValue = holdings.reduce((sum, holding) => sum + holding.value, 0);
  const totalChange24h = holdings.reduce((sum, holding) => {
    return sum + (holding.value * holding.change24h) / 100;
  }, 0);

  const sortedByChange = [...holdings].sort((a, b) => b.change24h - a.change24h);

  return {
    totalValue,
    change24h: totalChange24h,
    changePercent24h: (totalChange24h / (totalValue - totalChange24h)) * 100,
    bestPerformer: {
      symbol: sortedByChange[0].symbol,
      change: sortedByChange[0].change24h,
    },
    worstPerformer: {
      symbol: sortedByChange[sortedByChange.length - 1].symbol,
      change: sortedByChange[sortedByChange.length - 1].change24h,
    },
  };
};

const Dashboard = () => {
  const navigate = useNavigate();
  const [portfolioData] = useState(generatePortfolioData());
  const [performanceData] = useState(generatePerformanceData());
  const [holdings] = useState(mockHoldings);
  const [summary] = useState(calculatePortfolioSummary(mockHoldings));

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? "+" : ""}${value.toFixed(2)}%`;
  };

  const handleAssetClick = (symbol: string) => {
    navigate(`/asset/${symbol}`);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold text-foreground">Portfolio Dashboard</h1>
            <p className="text-xl text-muted-foreground">
              Track your investments in real-time
            </p>
          </div>
          <Button onClick={() => navigate("/")} variant="outline">
            Back to Charts
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.totalValue)}
              </div>
              <div
                className={`flex items-center text-xs ${
                  summary.changePercent24h >= 0 ? "text-green-600" : "text-red-600"
                }`}
              >
                {summary.changePercent24h >= 0 ? (
                  <ArrowUpIcon className="h-3 w-3 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-3 w-3 mr-1" />
                )}
                {formatPercent(summary.changePercent24h)} (
                {formatCurrency(summary.change24h)})
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">24h Change</CardTitle>
              {summary.changePercent24h >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600" />
              )}
            </CardHeader>
            <CardContent>
              <div
                className={`text-2xl font-bold ${
                  summary.changePercent24h >= 0 ? "text-green-600" : "text-red-600"
                }`}
              >
                {formatPercent(summary.changePercent24h)}
              </div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(Math.abs(summary.change24h))}
                {summary.changePercent24h >= 0 ? " gain" : " loss"}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Best Performer</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.bestPerformer.symbol}</div>
              <p className="text-xs text-green-600">
                +{summary.bestPerformer.change.toFixed(2)}% today
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Worst Performer</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.worstPerformer.symbol}</div>
              <p className="text-xs text-red-600">
                {summary.worstPerformer.change.toFixed(2)}% today
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Portfolio Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Portfolio Performance (30 Days)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[400px]">
              <AssetChart data={portfolioData} macdData={performanceData} />
            </div>
          </CardContent>
        </Card>

        {/* Holdings Table */}
        <Card>
          <CardHeader>
            <CardTitle>Your Holdings</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Asset</TableHead>
                  <TableHead>Symbol</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="text-right">24h Change</TableHead>
                  <TableHead className="text-right">Value</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {holdings.map((holding) => (
                  <TableRow
                    key={holding.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleAssetClick(holding.symbol)}
                  >
                    <TableCell className="font-medium">{holding.name}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">{holding.symbol}</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      {holding.quantity.toFixed(
                        holding.symbol.includes("BTC") || holding.symbol.includes("ETH")
                          ? 4
                          : 0
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(holding.currentPrice)}
                    </TableCell>
                    <TableCell className="text-right">
                      <span
                        className={
                          holding.change24h >= 0 ? "text-green-600" : "text-red-600"
                        }
                      >
                        {formatPercent(holding.change24h)}
                      </span>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {formatCurrency(holding.value)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
