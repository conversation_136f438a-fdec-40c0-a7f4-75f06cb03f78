import { <PERSON> } from "react-router-dom";
import { Asset<PERSON><PERSON> } from "@/components/AssetChart";
import { But<PERSON> } from "@/components/ui/button";
import { CandlestickData, HistogramData } from "lightweight-charts";

// Generate fake candlestick data
const generateFakeData = (): CandlestickData[] => {
  const data: CandlestickData[] = [];
  let basePrice = 100;
  
  for (let i = 0; i < 100; i++) {
    const time = new Date(2024, 0, 1 + i).getTime() / 1000;
    const volatility = 0.02;
    const change = (Math.random() - 0.5) * volatility * basePrice;
    
    const open = basePrice;
    const close = basePrice + change;
    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice;
    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice;
    
    data.push({
      time: time as any,
      open,
      high,
      low,
      close,
    });
    
    basePrice = close;
  }
  
  return data;
};

// Generate fake MACD data
const generateFakeMACDData = (): HistogramData[] => {
  const data: HistogramData[] = [];
  
  for (let i = 0; i < 100; i++) {
    const time = new Date(2024, 0, 1 + i).getTime() / 1000;
    const value = Math.sin(i * 0.1) * 2 + Math.random() * 0.5;
    
    data.push({
      time: time as any,
      value,
      color: value > 0 ? '#22c55e' : '#ef4444',
    });
  }
  
  return data;
};

const Index = () => {
  const candlestickData = generateFakeData();
  const macdData = generateFakeMACDData();

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-foreground">Signal - Portfolio Tracker</h1>
            <p className="text-xl text-muted-foreground">Technical analysis for your investments</p>
          </div>
          <Link to="/dashboard">
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              View Dashboard
            </Button>
          </Link>
        </div>
        
        <div className="bg-card rounded-lg border p-6">
          <h2 className="text-2xl font-semibold mb-4 text-card-foreground">BTC/USD Chart with MACD</h2>
          <AssetChart data={candlestickData} macdData={macdData} />
        </div>
      </div>
    </div>
  );
};

export default Index;
