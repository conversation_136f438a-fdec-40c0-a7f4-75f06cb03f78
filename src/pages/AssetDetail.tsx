import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/AssetChart';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, TrendingUp, TrendingDown, DollarSign, BarChart3 } from 'lucide-react';
import { CandlestickData, HistogramData } from 'lightweight-charts';

// Mock asset data
interface AssetInfo {
  symbol: string;
  name: string;
  currentPrice: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  high24h: number;
  low24h: number;
}

const mockAssetData: Record<string, AssetInfo> = {
  BTC: {
    symbol: 'BTC',
    name: 'Bitcoin',
    currentPrice: 43500,
    change24h: 2.3,
    volume24h: 23450000000,
    marketCap: 851000000000,
    high24h: 44200,
    low24h: 42800,
  },
  ETH: {
    symbol: 'ETH',
    name: 'Ethereum',
    currentPrice: 2650,
    change24h: -1.2,
    volume24h: 12300000000,
    marketCap: 318000000000,
    high24h: 2720,
    low24h: 2580,
  },
  AAPL: {
    symbol: 'AAPL',
    name: 'Apple Inc.',
    currentPrice: 185.50,
    change24h: 0.8,
    volume24h: 45600000,
    marketCap: 2890000000000,
    high24h: 187.20,
    low24h: 184.30,
  },
  GOOGL: {
    symbol: 'GOOGL',
    name: 'Alphabet Inc.',
    currentPrice: 142.30,
    change24h: -0.5,
    volume24h: 28900000,
    marketCap: 1780000000000,
    high24h: 144.10,
    low24h: 141.80,
  },
};

// Generate fake detailed price data for the asset
const generateAssetData = (symbol: string): CandlestickData[] => {
  const data: CandlestickData[] = [];
  const assetInfo = mockAssetData[symbol];
  let basePrice = assetInfo ? assetInfo.currentPrice * 0.95 : 100;
  
  for (let i = 0; i < 100; i++) {
    const time = new Date(2024, 0, 1 + i).getTime() / 1000;
    const volatility = symbol === 'BTC' ? 0.03 : symbol === 'ETH' ? 0.035 : 0.02;
    const change = (Math.random() - 0.5) * volatility * basePrice;
    
    const open = basePrice;
    const close = basePrice + change;
    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice;
    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice;
    
    data.push({
      time: time as any,
      open,
      high,
      low,
      close,
    });
    
    basePrice = close;
  }
  
  return data;
};

// Generate fake MACD data for the asset
const generateAssetMACDData = (): HistogramData[] => {
  const data: HistogramData[] = [];
  
  for (let i = 0; i < 100; i++) {
    const time = new Date(2024, 0, 1 + i).getTime() / 1000;
    const value = Math.sin(i * 0.1) * 2 + Math.random() * 0.5;
    
    data.push({
      time: time as any,
      value,
      color: value > 0 ? '#22c55e' : '#ef4444',
    });
  }
  
  return data;
};

const AssetDetail = () => {
  const { symbol } = useParams<{ symbol: string }>();
  const navigate = useNavigate();
  
  const assetInfo = symbol ? mockAssetData[symbol] : null;
  const candlestickData = symbol ? generateAssetData(symbol) : [];
  const macdData = generateAssetMACDData();

  if (!assetInfo) {
    return (
      <div className="min-h-screen bg-background p-6 flex items-center justify-center">
        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-2xl font-bold mb-4">Asset Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The asset "{symbol}" could not be found.
            </p>
            <Button onClick={() => navigate('/dashboard')}>
              Back to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  const formatLargeNumber = (value: number) => {
    if (value >= 1e12) {
      return `$${(value / 1e12).toFixed(2)}T`;
    } else if (value >= 1e9) {
      return `$${(value / 1e9).toFixed(2)}B`;
    } else if (value >= 1e6) {
      return `$${(value / 1e6).toFixed(2)}M`;
    }
    return formatCurrency(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => navigate('/dashboard')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <div className="flex items-center space-x-3">
                <h1 className="text-4xl font-bold text-foreground">{assetInfo.name}</h1>
                <Badge variant="secondary" className="text-lg px-3 py-1">
                  {assetInfo.symbol}
                </Badge>
              </div>
              <p className="text-xl text-muted-foreground">
                Detailed analysis and technical indicators
              </p>
            </div>
          </div>
        </div>

        {/* Price Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <Card className="md:col-span-2">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Price</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{formatCurrency(assetInfo.currentPrice)}</div>
              <div className={`flex items-center text-sm ${
                assetInfo.change24h >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {assetInfo.change24h >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercent(assetInfo.change24h)} (24h)
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">24h High</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold">{formatCurrency(assetInfo.high24h)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">24h Low</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold">{formatCurrency(assetInfo.low24h)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Volume (24h)</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold">{formatLargeNumber(assetInfo.volume24h)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Market Cap</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-xl font-bold">{formatLargeNumber(assetInfo.marketCap)}</div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Chart */}
        <Card>
          <CardHeader>
            <CardTitle>{assetInfo.name} ({assetInfo.symbol}) - Technical Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[500px]">
              <AssetChart data={candlestickData} macdData={macdData} />
            </div>
          </CardContent>
        </Card>

        {/* Analysis Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Market Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">Price Action</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Current Trend:</span>
                    <span className={assetInfo.change24h >= 0 ? 'text-green-600' : 'text-red-600'}>
                      {assetInfo.change24h >= 0 ? 'Bullish' : 'Bearish'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Support Level:</span>
                    <span>{formatCurrency(assetInfo.low24h * 0.98)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Resistance Level:</span>
                    <span>{formatCurrency(assetInfo.high24h * 1.02)}</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-3">Technical Indicators</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">MACD Signal:</span>
                    <span className="text-green-600">Bullish Crossover</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">RSI (14):</span>
                    <span>64.2 (Neutral)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Moving Average:</span>
                    <span>Above 20-day MA</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AssetDetail;