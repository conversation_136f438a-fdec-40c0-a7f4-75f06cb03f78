// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

const SUPABASE_URL = "https://oyekojzsteofnqqznuzq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95ZWtvanpzdGVvZm5xcXpudXpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0OTYwOTcsImV4cCI6MjA2ODA3MjA5N30.ahBfBMCyBYx9nqm4bEpfw9-_1LxLbWYHgPnxAwzF7xY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  },
});
